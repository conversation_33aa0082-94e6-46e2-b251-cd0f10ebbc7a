ffmpeg version 4.4.2-0ubuntu0.22.04.1 Copyright (c) 2000-2021 the FFmpeg developers
  built with gcc 11 (Ubuntu 11.2.0-19ubuntu1)
  configuration: --prefix=/usr --extra-version=0ubuntu0.22.04.1 --toolchain=hardened --libdir=/usr/lib/x86_64-linux-gnu --incdir=/usr/include/x86_64-linux-gnu --arch=amd64 --enable-gpl --disable-stripping --enable-gnutls --enable-ladspa --enable-libaom --enable-libass --enable-libbluray --enable-libbs2b --enable-libcaca --enable-libcdio --enable-libcodec2 --enable-libdav1d --enable-libflite --enable-libfontconfig --enable-libfreetype --enable-libfribidi --enable-libgme --enable-libgsm --enable-libjack --enable-libmp3lame --enable-libmysofa --enable-libopenjpeg --enable-libopenmpt --enable-libopus --enable-libpulse --enable-librabbitmq --enable-librubberband --enable-libshine --enable-libsnappy --enable-libsoxr --enable-libspeex --enable-libsrt --enable-libssh --enable-libtheora --enable-libtwolame --enable-libvidstab --enable-libvorbis --enable-libvpx --enable-libwebp --enable-libx265 --enable-libxml2 --enable-libxvid --enable-libzimg --enable-libzmq --enable-libzvbi --enable-lv2 --enable-omx --enable-openal --enable-opencl --enable-opengl --enable-sdl2 --enable-pocketsphinx --enable-librsvg --enable-libmfx --enable-libdc1394 --enable-libdrm --enable-libiec61883 --enable-chromaprint --enable-frei0r --enable-libx264 --enable-shared
  libavutil      56. 70.100 / 56. 70.100
  libavcodec     58.134.100 / 58.134.100
  libavformat    58. 76.100 / 58. 76.100
  libavdevice    58. 13.100 / 58. 13.100
  libavfilter     7.110.100 /  7.110.100
  libswscale      5.  9.100 /  5.  9.100
  libswresample   3.  9.100 /  3.  9.100
  libpostproc    55.  9.100 / 55.  9.100
Input #0, rawvideo, from 'pipe:':
  Duration: N/A, start: 0.000000, bitrate: 18432 kb/s
  Stream #0:0: Video: rawvideo (RGB[24] / 0x18424752), rgb24, 320x240, 18432 kb/s, 10 tbr, 10 tbn, 10 tbc
Stream mapping:
  Stream #0:0 -> #0:0 (rawvideo (native) -> h264 (libx264))
[libx264 @ 0x5a18d285dbc0] using cpu capabilities: MMX2 SSE2Fast SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2 AVX512
[libx264 @ 0x5a18d285dbc0] profile High, level 1.1, 4:2:0, 8-bit
[libx264 @ 0x5a18d285dbc0] 264 - core 163 r3060 5db6aa6 - H.264/MPEG-4 AVC codec - Copyleft 2003-2021 - http://www.videolan.org/x264.html - options: cabac=1 ref=1 deblock=1:0:0 analyse=0x3:0x113 me=hex subme=2 psy=1 psy_rd=1.00:0.00 mixed_ref=0 me_range=16 chroma_me=1 trellis=0 8x8dct=1 cqm=0 deadzone=21,11 fast_pskip=1 chroma_qp_offset=0 threads=3 lookahead_threads=3 sliced_threads=1 slices=3 nr=0 decimate=1 interlaced=0 bluray_compat=0 constrained_intra=0 bframes=0 weightp=1 keyint=15 keyint_min=1 scenecut=40 intra_refresh=0 rc=crf mbtree=0 crf=23.0 qcomp=0.60 qpmin=0 qpmax=69 qpstep=4 ip_ratio=1.40 aq=1:1.00
Output #0, mp4, to 'eval_video/policyblock_hammer_beat_D435/seed20007/20250708_054103.mp4':
  Metadata:
    encoder         : Lavf58.76.100
  Stream #0:0: Video: h264 (avc1 / 0x31637661), yuv420p(tv, progressive), 320x240, q=2-31, 10 fps, 10240 tbn
    Metadata:
      encoder         : Lavc58.134.100 libx264
    Side data:
      cpb: bitrate max/min/avg: 0/0/0 buffer size: 0 vbv_delay: N/A
frame=    1 fps=0.0 q=20.0 size=       0kB time=00:00:00.00 bitrate=3918.4kbits/s speed=N/A    
frame=    4 fps=0.0 q=19.0 size=       0kB time=00:00:00.30 bitrate=   1.3kbits/s speed=0.493x    
frame=    5 fps=2.8 q=19.0 size=       0kB time=00:00:00.40 bitrate=   1.0kbits/s speed=0.223x    
frame=    7 fps=3.0 q=19.0 size=       0kB time=00:00:00.60 bitrate=   0.6kbits/s speed=0.255x    
frame=    9 fps=2.5 q=19.0 size=       0kB time=00:00:00.80 bitrate=   0.5kbits/s speed=0.218x    
frame=   11 fps=2.5 q=20.0 size=       0kB time=00:00:01.00 bitrate=   0.4kbits/s speed=0.228x    
frame=   12 fps=2.1 q=20.0 size=       0kB time=00:00:01.10 bitrate=   0.3kbits/s speed=0.195x    
frame=   14 fps=2.2 q=17.0 size=       0kB time=00:00:01.30 bitrate=   0.3kbits/s speed=0.202x    
frame=   15 fps=2.0 q=23.0 size=       0kB time=00:00:01.40 bitrate=   0.3kbits/s speed=0.184x    
frame=   17 fps=2.0 q=24.0 size=       0kB time=00:00:01.60 bitrate=   0.2kbits/s speed=0.188x    
frame=   18 fps=1.8 q=23.0 size=       0kB time=00:00:01.70 bitrate=   0.2kbits/s speed=0.173x    
frame=   20 fps=1.8 q=22.0 size=       0kB time=00:00:01.90 bitrate=   0.2kbits/s speed=0.166x    
frame=   22 fps=1.8 q=23.0 size=       0kB time=00:00:02.10 bitrate=   0.2kbits/s speed=0.173x    
frame=   23 fps=1.8 q=22.0 size=       0kB time=00:00:02.20 bitrate=   0.2kbits/s speed=0.168x    
frame=   25 fps=1.8 q=22.0 size=       0kB time=00:00:02.40 bitrate=   0.2kbits/s speed=0.171x    
frame=   26 fps=1.7 q=21.0 size=       0kB time=00:00:02.50 bitrate=   0.2kbits/s speed=0.165x    
frame=   28 fps=1.7 q=21.0 size=       0kB time=00:00:02.70 bitrate=   0.1kbits/s speed=0.168x    
frame=   29 fps=1.7 q=19.0 size=       0kB time=00:00:02.80 bitrate=   0.1kbits/s speed=0.163x    
frame=   30 fps=1.6 q=23.0 size=       0kB time=00:00:02.90 bitrate=   0.1kbits/s speed=0.15x    
frame=   31 fps=1.5 q=23.0 size=       0kB time=00:00:03.00 bitrate=   0.1kbits/s speed=0.142x    
frame=   32 fps=1.5 q=23.0 size=       0kB time=00:00:03.10 bitrate=   0.1kbits/s speed=0.143x    
frame=   33 fps=1.4 q=22.0 size=       0kB time=00:00:03.20 bitrate=   0.1kbits/s speed=0.139x    
frame=   34 fps=1.4 q=22.0 size=       0kB time=00:00:03.30 bitrate=   0.1kbits/s speed=0.14x    
frame=   35 fps=1.4 q=23.0 size=       0kB time=00:00:03.40 bitrate=   0.1kbits/s speed=0.136x    
frame=   36 fps=1.4 q=23.0 size=       0kB time=00:00:03.50 bitrate=   0.1kbits/s speed=0.137x    
frame=   37 fps=1.4 q=22.0 size=       0kB time=00:00:03.60 bitrate=   0.1kbits/s speed=0.134x    
frame=   38 fps=1.4 q=22.0 size=       0kB time=00:00:03.70 bitrate=   0.1kbits/s speed=0.135x    
frame=   39 fps=1.4 q=21.0 size=       0kB time=00:00:03.80 bitrate=   0.1kbits/s speed=0.132x    
frame=   40 fps=1.4 q=21.0 size=       0kB time=00:00:03.90 bitrate=   0.1kbits/s speed=0.133x    
frame=   40 fps=1.3 q=21.0 size=       0kB time=00:00:03.90 bitrate=   0.1kbits/s speed=0.127x    
frame=   40 fps=1.3 q=21.0 Lsize=      78kB time=00:00:03.90 bitrate= 164.0kbits/s speed=0.126x    
video:77kB audio:0kB subtitle:0kB other streams:0kB global headers:0kB muxing overhead: 1.378637%
[libx264 @ 0x5a18d285dbc0] frame I:3     Avg QP:14.18  size:  4573
[libx264 @ 0x5a18d285dbc0] frame P:37    Avg QP:17.58  size:  1743
[libx264 @ 0x5a18d285dbc0] mb I  I16..4: 39.6% 20.7% 39.8%
[libx264 @ 0x5a18d285dbc0] mb P  I16..4: 13.3% 10.3%  3.0%  P16..4: 27.0% 13.9%  7.7%  0.0%  0.0%    skip:24.8%
[libx264 @ 0x5a18d285dbc0] 8x8 transform intra:34.4% inter:11.8%
[libx264 @ 0x5a18d285dbc0] coded y,uvDC,uvAC intra: 47.9% 24.4% 10.6% inter: 21.2% 5.0% 1.0%
[libx264 @ 0x5a18d285dbc0] i16 v,h,dc,p: 50% 26% 18%  5%
[libx264 @ 0x5a18d285dbc0] i8 v,h,dc,ddl,ddr,vr,hd,vl,hu: 16% 25% 27%  5%  7%  6%  6%  3%  5%
[libx264 @ 0x5a18d285dbc0] i4 v,h,dc,ddl,ddr,vr,hd,vl,hu: 21% 24% 21%  5%  8%  6%  6%  4%  5%
[libx264 @ 0x5a18d285dbc0] i8c dc,h,v,p: 78% 14%  7%  1%
[libx264 @ 0x5a18d285dbc0] Weighted P-Frames: Y:0.0% UV:0.0%
[libx264 @ 0x5a18d285dbc0] kb/s:156.46
