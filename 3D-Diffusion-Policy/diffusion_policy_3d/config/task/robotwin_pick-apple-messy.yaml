name: pick_apple_messy

shape_meta: &shape_meta
  # acceptable types: rgb, low_dim
  obs:
    point_cloud:
      shape: [1024, 6]
      type: point_cloud
    agent_pos:
      shape: [14]
      type: low_dim
  action:
    shape: [14]

env_runner:
  _target_: diffusion_policy_3d.env_runner.robotwin_runner.RobotwinRunner
  eval_episodes: 20
  max_steps: 400
  n_obs_steps: ${n_obs_steps}
  n_action_steps: ${n_action_steps}
  fps: 10
  seed: 42 # 注意这里的seed应该要跟dataset中保持一致，这个seed用于eval
  task_name: pick_apple_messy
  use_point_crop: ${policy.use_point_crop}

dataset:
  _target_: diffusion_policy_3d.dataset.robotwin_dataset.RobotwinDataset
  zarr_path: data/robotwin/pick_apple_messy.zarr
  horizon: ${horizon}
  pad_before: ${eval:'${n_obs_steps}-1'}
  pad_after: ${eval:'${n_action_steps}-1'}
  seed: 42
  val_ratio: 0.02
  max_train_episodes: null
